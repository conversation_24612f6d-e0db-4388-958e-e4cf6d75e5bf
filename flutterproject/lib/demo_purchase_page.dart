import 'package:flutter/material.dart';
import 'order/purchase_page/purchase_order_create.dart';

class DemoPurchasePage extends StatelessWidget {
  const DemoPurchasePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '代买页面演示',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const PurchaseOrderPage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

void main() {
  runApp(const DemoPurchasePage());
}
